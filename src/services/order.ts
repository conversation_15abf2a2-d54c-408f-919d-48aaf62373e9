import { apiService } from "./api";
const ENDPOINT = "v1/admin/order";

export const orderService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listOrders: build.query<TReponsePaging<TOrder>, TQueryAPI>({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TReponsePaging<TOrder>) => {
        const data = rawResult.data?.map((item: TOrder, index) => ({
          ...item,
          _rowIndex: index + 1,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
    getOrderById: build.query<TOrder, string>({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TOrder) => {
        return rawResult;
      },
    }),
    getDataToCreateOrder: build.query<
      { info: { permissions: TPermission[] } },
      TQueryAPI
    >({
      query: () => {
        return {
          url: ENDPOINT + "/create",
          method: "GET",
        };
      },
    }),
    createOrder: build.mutation<
      TOrder,
      Pick<TOrder, 'name'> & Partial<TOrder>
    >({
      query: (payload) => {
        return {
          url: ENDPOINT,
          method: "POST",
          data: payload,
        };
      },
      transformResponse: (rawResult: TOrder) => {
        return rawResult;
      },
    }),
    getDataToUpdateOrder: build.query<
      { data: TOrder, info: { permissions: TPermission[] } },
      string
    >({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id + "/edit",
          method: "GET",
        };
      },
    }),
    updateOrder: build.mutation<
      TOrder,
      Partial<TOrder>
    >({
      query: ({ id, ...payload }) => {
        return {
          url: ENDPOINT + `/${id}`,
          method: "PUT",
          data: payload,
        };
      },
      transformResponse: (rawResult: TOrder) => {
        return rawResult;
      },
    }),
    deleteOrderById: build.query<
      TOrder,
      string
    >({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "DELETE",
        };
      },
      transformResponse: (rawResult: TOrder) => {
        return rawResult;
      },
    }),
    syncOrderById: build.mutation<
      any,
      any
    >({
      query: ({ id, ...payload }) => {
        return {
          url: ENDPOINT + `/${id}/sync`,
          method: "PUT",
          data: payload,
        };
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListOrdersQuery,
  useLazyGetOrderByIdQuery,
  useLazyGetDataToCreateOrderQuery,
  useLazyGetDataToUpdateOrderQuery,
  useCreateOrderMutation,
  useUpdateOrderMutation,
  useLazyDeleteOrderByIdQuery,
  useSyncOrderByIdMutation,
} = orderService;
