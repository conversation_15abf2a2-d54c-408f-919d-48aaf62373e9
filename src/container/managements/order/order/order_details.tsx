import moment from "moment";
import { FC, Fragment, useEffect, useState } from "react";
import { Badge, Button, Card, Col, Dropdown, Form, InputGroup, Modal, Row, Table } from "react-bootstrap";
import { Link, useParams } from "react-router-dom";
import CreatableSelect from "react-select/creatable";
import Swal from "sweetalert2";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import Card<PERSON>eader<PERSON>ithBack from "../../../../components/table-title/card-header-with-back";
import { useLazyGetOrderByIdQuery, useSyncOrderByIdMutation, useUpdateOrderMutation } from "../../../../services/order";
import { currencySymbol } from "../../../../utils/constant/currency";
import { getAllErrorMessages } from "../../../../utils/errors";


interface ManagementOrderDetailsProps { }

const ManagementOrderDetails: FC<ManagementOrderDetailsProps> = () => {
  const { id } = useParams();

  const [isLoading, setIsLoading] = useState(false)
  // const [errors, setErrors] = useState<any>({});

  const [order, setOrder] = useState<Partial<TOrder>>({})

  const [getOrder] = useLazyGetOrderByIdQuery()

  const prepareReceivedData = (data: TOrder) => {
    setOrder(data)

    // if (data.fulfillment) {
    //   const numbers = data.fulfillment.trackingNumbers.split(',')
    //   const urls = data.fulfillment.trackingUrls.split(',')

    //   const longestArray = numbers.length > urls.length ? numbers : urls

    //   const trackings: { id: number, number: string, url: string }[] = []
    //   for (let i = 0; i < longestArray.length; i++) {
    //     trackings.push({ id: Date.now() + i, number: numbers[i] || '', url: urls[i] || '' })
    //   }

    //   setTrackingInfo({
    //     trackingCompany: data.fulfillment.trackingCompany || '',
    //     trackings,
    //   })
    // }
  }

  useEffect(() => {
    if (!id) { return }

    setIsLoading(true)
    getOrder(id || "")
      .unwrap()
      .then((res) => {
        prepareReceivedData(res)

      })
      .catch((error) => { Swal.fire(getAllErrorMessages(error).messages[0]) })
      .finally(() => {
        setIsLoading(false)
      });
  }, [id]);

  // const [trackingInfoModalShown, setTrackingInfoModalShown] = useState(false)
  const [shippingAddressModalShown, setShippingAddressModalShown] = useState(false)
  const [billingAddressModalShown, setBillingAddressModalShown] = useState(false)

  const [syncOrder, { isLoading: isSyncing }] = useSyncOrderByIdMutation()

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Card className="custom-card">
        <Card.Header>
          <Card.Title>
            <div className="d-flex align-items-center">
              <CardHeaderWithBack
                title={`Order ${order.name || ''}`}
                route=''
              />
              <div>
                <Badge className="text-capitalize rounded-pill bg-primary-transparent mx-1">
                  {order.zurnoStatus}
                </Badge>
              </div>
            </div>
          </Card.Title>
          <Card.Subtitle>
            <InputGroup>
              <Button
                onClick={() => {
                  syncOrder({ id })
                    .unwrap()
                    .then((res) => Swal.fire({ icon: 'success', title: res }))
                    .catch((er) => Swal.fire('Error!', getAllErrorMessages(er).messages[0], 'error'))

                }}
              >
                {
                  isSyncing
                    ? <i className="spinner-border spinner-border-sm mx-2" />
                    : "Sync"
                }
              </Button>
              <Dropdown>
                <Dropdown.Toggle className="btn-sm dropdown-toggle-split" />
                <Dropdown.Menu>
                  <Dropdown.Item
                    className="btn-sm"
                    onClick={() => {
                      syncOrder({ id, async: true })
                        .unwrap()
                        .then((res) => Swal.fire({ icon: 'success', title: res }))
                        .catch((er) => Swal.fire('Error!', getAllErrorMessages(er).messages[0], 'error'))

                    }}
                  >
                    Async
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </InputGroup>
          </Card.Subtitle>
          <Card.Subtitle>
            <span className="fw-bold fs-14 ms-auto">
              {moment(order.createdAt).format('YYYY-MM-DD')}
              <div className="text-end">
              </div>
              <div className="text-end">
                {moment(order.createdAt).format('HH:mm:ss')}
              </div>
            </span>
          </Card.Subtitle>
        </Card.Header>
      </Card>
      <Row>
        <Col lg={8}>
          <Card>
            <Card.Body className="overflow-auto">
              <Table className="table table-bordered text-nowrap border-bottom mb-3">
                <thead>
                  <tr>
                    <th className="text-center">Image</th>
                    <th className="text-center">Product</th>
                    <th className="text-center">Vendor</th>
                    <th className="text-center">Quantity</th>
                    <th className="text-center">Price</th>
                    <th className="text-center">Discount</th>
                    <th className="text-center">Tax</th>
                    <th className="text-center">Value</th>
                  </tr>
                </thead>
                <tbody>
                  {order.orderDetails?.map((details, index) => (
                    <Fragment key={index}>
                      <OrderDetailsProduct
                        details={details}
                        order={order}
                      />
                    </Fragment>
                  ))}
                </tbody>
              </Table>
            </Card.Body>
          </Card>

          {
            order.fulfillments?.map((fulfillment, index) => (
              <Fragment key={index}>
                <OrderFulfillments
                  order={order}
                  fulfillment={fulfillment}
                />
              </Fragment>
            ))

          }
        </Col>
        <Col lg={4}>
          <Card>
            <Card.Body>
              <Row>
                <Col>
                  <div className="fw-bold">Subtotal Price</div>
                  <div className="fw-bold">Total Discounts</div>
                  <div className="fw-bold">Total Tax</div>
                  <div className="fw-bold">Total Shipping</div>
                  <div className="fw-bold">Total Price</div>
                  <div className="fw-bold">Current Total Price</div>
                </Col>
                <Col>
                  <div>{order.subtotalPrice}</div>
                  <div>{order.totalDiscounts}</div>
                  <div>{order.totalTax}</div>
                  <div>{order.totalShipping}</div>
                  <div>{order.totalPrice?.toFixed(2)}</div>
                  <div>{order.currentTotalPrice?.toFixed(2)}</div>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* <Card className="custom-card">
            <Card.Header>
              <Card.Title>
                Shipping Details
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <span
                  style={{ cursor: 'pointer' }}
                  className="text-primary"
                  onClick={() => setTrackingInfoModalShown(true)}
                >
                  Tracking Info
                </span>
              </div>

              {
                trackingInfo &&
                <Fragment>
                  <Row>
                    <Col lg={6} className="mb-3">
                      <span className="fw-bold">Shipping Carrier</span>
                    </Col>
                    <Col lg={6} className="mb-3">
                      <span className="text-muted">{trackingInfo.trackingCompany}</span>
                    </Col>
                  </Row>

                  {
                    trackingInfo.trackings.map((tracking, index) => (
                      <Fragment key={index}>
                        <Row>
                          <Col lg={6} className="mb-3">
                            <span className="fw-bold">
                              {
                                index == 0
                                  ? `Tracking Number${trackingInfo.trackings?.length > 1 ? 's' : ''}`
                                  : ''
                              }
                            </span>
                          </Col>
                          <Col lg={6} className="mb-3">
                            <Link
                              to={tracking.url}
                              className="text-decoration-underline text-info text-muted mb-3"
                            >
                              {tracking.number}
                            </Link>
                          </Col>
                        </Row>
                      </Fragment>
                    ))
                  }
                </Fragment>
              }
            </Card.Body>
          </Card> */}

          <Card className="custom-card">
            <Card.Header>
              <Card.Title>
                Customer Details
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <div>
                  <span className="fw-bold">Name: </span>
                  <span>{order.user?.fullname}</span>
                </div>
                <div>
                  <span className="fw-bold">Email: </span>
                  <span>{order.user?.email}</span>
                </div>
              </div>

              <Row>
                <Col className="mb-3">
                  <div className="text-primary">
                    <span
                      style={{ cursor: 'pointer' }}
                      onClick={() => setShippingAddressModalShown(true)}
                    >
                      Ship To
                    </span>
                  </div>
                  {
                    order?.shipping
                      ?
                      <div>
                        <div>
                          {order.shipping?.firstName} {order.shipping?.lastName}
                        </div>
                        <div>
                          {order.shipping?.address1}, {order.shipping?.address2}
                        </div>
                        <div>
                          {order.shipping?.city}, {order.shipping?.province} ({order.shipping?.provinceCode})
                        </div>
                        <div>
                          {order.shipping?.zip}, {order.shipping?.country} ({order.shipping?.countryCode})
                        </div>
                      </div>
                      :
                      <div>
                        No Shipping Address
                      </div>
                  }
                </Col>
                <Col className="mb-3">
                  <div className="text-primary">
                    <span
                      style={{ cursor: 'pointer' }}
                      onClick={() => setBillingAddressModalShown(true)}
                    >
                      Bill To
                    </span>
                  </div>
                  {
                    order.billing
                      ?
                      <div>
                        <div>
                          {order.billing?.firstName} {order.billing?.lastName}
                        </div>
                        <div>
                          {order.billing?.address1}, {order.billing?.address2}
                        </div>
                        <div>
                          {order.billing?.city}, {order.billing?.province} ({order.billing?.provinceCode})
                        </div>
                        <div>
                          {order.billing?.zip}, {order.billing?.country} ({order.billing?.countryCode})
                        </div>
                      </div>
                      :
                      <div>
                        No Billing Address
                      </div>
                  }
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row >

      <ShippingAddressModal
        show={shippingAddressModalShown}
        onHide={() => setShippingAddressModalShown(false)}
        order={order}
        setOrder={setOrder}
        vendorOrder={order}
      />

      <BillingAddressModal
        show={billingAddressModalShown}
        onHide={() => setBillingAddressModalShown(false)}
        order={order}
      />
    </Fragment >
  );
};

const OrderDetailsProduct = ({ order, details }: any) => {

  const [productNameHovered, setProductNameHovered] = useState(false)

  const calculateValues = () => {
    const price = details.price.toFixed(2)
    const discount = details.discount.toFixed(2)
    const tax = details.tax.toFixed(2)
    const value = (details.price * details.quantity - details.discount + details.tax).toFixed(2)

    return {
      price,
      discount,
      tax,
      value,
    }
  }

  return (
    <Fragment>
      <tr>
        <td className="text-center">
          <div className="avatar avatar-xxl bg-dark-transparent">
            {
              details.variant?.image?.src
                ?
                <img
                  src={details.variant?.image?.src || ''}
                  className=""
                />
                : "IMG"
            }
          </div>
        </td>
        <td className="text-center">
          <div>{details.variant?.sku}</div>
          <div>{details.variant?.barcode}</div>
          <div>{details.variant?.title}</div>
          <div>
            <Link
              to={`/managements-products/${details.variant?.productId}`}
              className={`text-${productNameHovered ? 'info text-decoration-underline' : "primary"}`}
              onMouseEnter={() => setProductNameHovered(true)}
              onMouseLeave={() => setProductNameHovered(false)}
            >
              {details.variant?.product?.title}
            </Link>
          </div>
        </td>
        <td className="text-center">{details.variant?.product?.vendor?.companyName}</td>
        <td className="text-center">{details.quantity}</td>
        <td className="text-center">{currencySymbol[order?.currency || '']} {calculateValues().price}</td>
        <td className="text-center">{currencySymbol[order?.currency || '']} {calculateValues().discount}</td>
        <td className="text-center">{currencySymbol[order?.currency || '']} {calculateValues().tax}</td>
        <td className="text-center">{currencySymbol[order.currency || '']} {calculateValues().value}</td>
      </tr>
    </Fragment>
  )
}

const OrderFulfillments = ({ order, fulfillment }: any) => {

  const [trackingInfoModalShown, setTrackingInfoModalShown] = useState(false)

  const [trackingInfo, setTrackingInfo] = useState<{
    trackingCompany: string,
    trackings: {
      id: number,
      number: string,
      url: string
    }[]
  } | null>(null)

  useEffect(() => {
    if (fulfillment) {
      const numbers = fulfillment.trackingNumbers.split(',')
      const urls = fulfillment.trackingUrls.split(',')

      const longestArray = numbers.length > urls.length ? numbers : urls

      const trackings: { id: number, number: string, url: string }[] = []
      for (let i = 0; i < longestArray.length; i++) {
        trackings.push({ id: Date.now() + i, number: numbers[i] || '', url: urls[i] || '' })
      }

      setTrackingInfo({
        trackingCompany: fulfillment.trackingCompany || '',
        trackings,
      })
    }
  }, [fulfillment])

  return (
    <Fragment>
      <Card className="custom-card">
        <Card.Header>
          <Card.Title>
            <span className="text-uppercase">
              {fulfillment.status}
            </span>
          </Card.Title>
          <Card.Subtitle>
            <span
              style={{ cursor: 'pointer' }}
              className="text-primary"
              onClick={() => setTrackingInfoModalShown(true)}
            >
              Tracking Info
            </span>
          </Card.Subtitle>
        </Card.Header>
        <Card.Body className="overflow-auto">
          <Table className="table table-bordered text-nowrap border-bottom mb-3">
            <thead>
              <tr>
                <th className="text-center">Image</th>
                <th className="text-center">Product</th>
                <th className="text-center">Vendor</th>
                <th className="text-center">Quantity</th>
                <th className="text-center">Price</th>
                <th className="text-center">Discount</th>
                <th className="text-center">Tax</th>
                <th className="text-center">Value</th>
              </tr>
            </thead>
            <tbody>
              {fulfillment.orderDetails?.map((details, index) => (
                <Fragment key={index}>
                  <OrderDetailsProduct
                    details={details}
                    order={order}
                  />
                </Fragment>
              ))}
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      <TrackingInfoModal
        show={trackingInfoModalShown}
        onHide={() => setTrackingInfoModalShown(false)}
        order={order}
        trackingInfo={trackingInfo}
        setTrackingInfo={setTrackingInfo}
      />
    </Fragment>
  )
}

const TrackingInfoModal = ({
  show,
  onHide,

  order,

  trackingInfo,
  setTrackingInfo,
}: any) => {

  const [innerTrackingCompany, setInnerTrackingCompany] = useState(trackingInfo?.trackingCompany || '')
  const [innerTrackings, setInnerTrackings] = useState(trackingInfo?.trackings || [{
    id: Date.now(),
    number: '',
    url: '',
  }])

  useEffect(() => {
    if (!trackingInfo?.trackingCompany) { return }
    setInnerTrackingCompany(trackingInfo?.trackingCompany)
  }, [trackingInfo?.trackingCompany])

  useEffect(() => {
    if (!trackingInfo?.trackings) { return }
    setInnerTrackings(trackingInfo?.trackings)
  }, [trackingInfo?.trackings])

  const [innerTrackingErrors, setInnerTrackingErrors] = useState({
    trackingCompany: false,
    trackings: [{ id: '', number: false, url: false }],
  })

  const checkUpdateError = () => {
    let hasError = false, trackingCompanyError = false, trackingsError: any = []
    if (!innerTrackingCompany) {
      trackingCompanyError = true
      hasError = true
    }
    for (const tracking of innerTrackings) {
      const numberError = !tracking.number
      const urlError = !tracking.url

      trackingsError.push({ id: tracking.id, number: numberError, url: urlError })

      if (numberError || urlError) { hasError = true }
    }

    if (hasError) {
      setInnerTrackingErrors({ trackingCompany: trackingCompanyError, trackings: trackingsError })
    }

    return hasError
  }

  const [updateOrder, { isLoading }] = useUpdateOrderMutation()
  const handleUpdateClick = () => {
    if (checkUpdateError()) { return }

    const trackingInfo = { trackingCompany: innerTrackingCompany, trackings: innerTrackings }

    // @ts-ignore
    updateOrder({ id: order.id, trackingInfo })
      .unwrap()
      .then(() => {
        setTrackingInfo({
          trackingCompany: innerTrackingCompany,
          trackings: innerTrackings,
        })
        onHide?.()
      })
      .catch((error) => Swal.fire(getAllErrorMessages(error).messages[0]))

  }

  const handleCancelClick = () => {
    setInnerTrackingErrors({
      trackingCompany: false,
      trackings: [],
    })

    setInnerTrackingCompany(trackingInfo?.trackingCompany || '')
    setInnerTrackings(trackingInfo?.trackings || [{
      id: Date.now(),
      number: '',
      url: '',
    }])
    onHide?.()
  }

  const handleAddTrackingNumberClick = () => {
    setInnerTrackings([
      ...innerTrackings,
      {
        id: Date.now() + innerTrackings.length,
        number: '',
        url: ''
      },
    ])
  }

  const shippingCarrierOptions = [
    {
      label: 'Frequently Used Carriers',
      options: [
        { label: 'UPS', value: 'UPS' },
        { label: 'FedEx', value: 'FedEx' },
        { label: 'DHL Express', value: 'DHL Express' },
        { label: 'USPS', value: 'USPS' },
      ]
    }
  ]

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => { onHide?.() }}

        centered
        size='lg'
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Order {order.order?.name} Tracking Info
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-3">
            <Form.Label>Shipping Carrier</Form.Label>
            <CreatableSelect
              classNamePrefix='Select2'
              className={innerTrackingErrors.trackingCompany ? `border border-danger` : ''}
              options={shippingCarrierOptions}
              value={{ label: innerTrackingCompany, value: innerTrackingCompany }}
              onChange={(value) => {
                setInnerTrackingErrors({
                  ...innerTrackingErrors,
                  trackingCompany: false
                })
                setInnerTrackingCompany(value?.label || '')
              }}
            />
          </div>

          {
            innerTrackings.map((tracking, index) => (
              <Fragment key={index}>
                <Row>
                  <Col lg={5}>
                    <Form.Label>Tracking Number</Form.Label>
                  </Col>
                  <Col lg={6}>
                    <Form.Label>Tracking Url</Form.Label>
                  </Col>
                </Row>

                <Row>
                  <Col lg={5} className="mb-3">
                    <Form.Control
                      className={innerTrackingErrors.trackings[index]?.number ? `border border-danger` : ''}
                      value={tracking.number}
                      onChange={(e) => {
                        setInnerTrackingErrors({
                          ...innerTrackingErrors,
                          trackings: innerTrackingErrors.trackings.map(err =>
                            err.id != tracking.id ? err : { ...err, number: false }
                          )
                        })
                        setInnerTrackings(
                          innerTrackings.map(
                            track => track.id != tracking.id
                              ? track
                              : { ...track, number: e.target.value || '' })
                        )
                      }}
                    />
                  </Col>
                  <Col lg={innerTrackings.length > 1 ? 6 : 7} className="mb-3">
                    <Form.Control
                      className={innerTrackingErrors.trackings[index]?.url ? `border border-danger` : ''}
                      value={tracking.url}
                      onChange={(e) => {
                        setInnerTrackingErrors({
                          ...innerTrackingErrors,
                          trackings: innerTrackingErrors.trackings.map(err =>
                            err.id != tracking.id ? err : { ...err, url: false }
                          )
                        })
                        setInnerTrackings(
                          innerTrackings.map(
                            track => track.id != tracking.id
                              ? track
                              : { ...track, url: e.target.value || '' })
                        )
                      }}
                    />
                  </Col>
                  {
                    innerTrackings.length > 1 &&
                    <Col lg={1} className="mb-3">
                      <Button
                        variant="danger-light"
                        className="btn"
                        onClick={() => setInnerTrackings(innerTrackings.filter(track => track.id != tracking.id))}
                      >
                        <i className="bi-trash" />
                      </Button>
                    </Col>
                  }
                </Row>
              </Fragment>
            ))
          }
          <div className="d-grid">
            <Button
              variant="primary-light"
              className="btn-sm"
              onClick={handleAddTrackingNumberClick}
            >
              <i className="bi-plus-lg me-1" />
              Add Another Tracking Number
            </Button>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleUpdateClick}>
            {
              isLoading
                ? <i className="spinner-border spinner-border-sm mx-3" />
                : 'Update'
            }
          </Button>
          <Button
            variant=""
            className="btn-light border-dark ms-2"
            onClick={handleCancelClick}
          >
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </Fragment >
  )
}

const ShippingAddressModal = ({
  show,
  onHide,

  order,
  setOrder,
  vendorOrder
}: any) => {

  const [shippingAddress, setShippingAddress] = useState<Partial<TAddress>>({})

  useEffect(() => {
    if (order?.shipping) { setShippingAddress(order.shipping) }
  }, [order?.shipping])

  const [updateOrder, { isLoading }] = useUpdateOrderMutation()
  const handleUpdateClick = () => {

    // @ts-ignore
    updateOrder({ id: vendorOrder.id, shippingAddress })
      .unwrap()
      .then(() => {
        setOrder({ ...order, shipping: shippingAddress })
        onHide?.()
      })
      .catch((error) => Swal.fire(getAllErrorMessages(error).messages[0]))

  }

  const handleCancelClick = () => {
    setShippingAddress(order?.shipping)
    onHide?.()
  }

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => { onHide?.() }}

        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Order {order?.name} Ship To
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">First Name</Form.Label>
              <Form.Control
                value={order?.shipping?.firstName}
                onChange={(e) => setShippingAddress({ ...shippingAddress, firstName: e.target.value })}
              />
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Last Name</Form.Label>
              <Form.Control
                value={order?.shipping?.lastName}
                onChange={(e) => setShippingAddress({ ...shippingAddress, lastName: e.target.value })}
              />
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Phone</Form.Label>
              <Form.Control
                value={order?.shipping?.phone}
                onChange={(e) => setShippingAddress({ ...shippingAddress, phone: e.target.value })}
              />
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Company</Form.Label>
              <Form.Control
                value={order?.shipping?.company}
                onChange={(e) => setShippingAddress({ ...shippingAddress, company: e.target.value })}
              />
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Address 1</Form.Label>
              <Form.Control
                value={order?.shipping?.address1}
                onChange={(e) => setShippingAddress({ ...shippingAddress, address1: e.target.value })}
              />
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Address 2</Form.Label>
              <Form.Control
                value={order?.shipping?.address2}
                onChange={(e) => setShippingAddress({ ...shippingAddress, address2: e.target.value })}
              />
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">City</Form.Label>
              <Form.Control
                value={order?.shipping?.city}
                onChange={(e) => setShippingAddress({ ...shippingAddress, city: e.target.value })}
              />
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Zip</Form.Label>
              <Form.Control
                value={order?.shipping?.zip}
                onChange={(e) => setShippingAddress({ ...shippingAddress, zip: e.target.value })}
              />
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Province</Form.Label>
              <Form.Control
                value={order?.shipping?.province}
                onChange={(e) => setShippingAddress({ ...shippingAddress, province: e.target.value })}
              />
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Country</Form.Label>
              <Form.Control
                value={order?.shipping?.country}
                onChange={(e) => setShippingAddress({ ...shippingAddress, country: e.target.value })}
              />
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Province Code</Form.Label>
              <div className="text-muted">{order?.shipping?.provinceCode}</div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Country Code</Form.Label>
              <div className="text-muted">{order?.shipping?.countryCode}</div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">Latitude</Form.Label>
              <div className="text-muted">{order?.shipping?.latitude}</div>
            </Col>
            <Col className="mb-3">
              <Form.Label>Longitude</Form.Label>
              <div className="text-muted">{order?.shipping?.longitude}</div>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleUpdateClick}>
            {
              isLoading
                ? <i className="spinner-border spinner-border-sm mx-3" />
                : 'Update'
            }
          </Button>
          <Button
            variant=""
            className="btn-light border-dark ms-2"
            onClick={handleCancelClick}
          >
            Cancel
          </Button>
        </Modal.Footer>
      </Modal>
    </Fragment>
  )
}

const BillingAddressModal = ({
  show,
  onHide,

  order,
}: any) => {

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => { onHide?.() }}

        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Order {order?.name} Bill To
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                First Name
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.firstName}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Last Name

              </Form.Label>
              <div className="text-muted">
                {order?.billing?.lastName}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Phone
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.phone}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Company
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.company}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Address 1
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.address1}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Address 2
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.address2}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                City
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.city}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Zip
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.zip}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Province
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.province}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Country
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.country}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Province Code
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.provinceCode}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Country Code
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.countryCode}
              </div>
            </Col>
          </Row>
          <Row>
            <Col className="mb-3">
              <Form.Label className="fw-bold">
                Latitude
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.latitude}
              </div>
            </Col>
            <Col className="mb-3">
              <Form.Label>
                Longitude
              </Form.Label>
              <div className="text-muted">
                {order?.billing?.longitude}
              </div>
            </Col>
          </Row>
        </Modal.Body>
      </Modal>
    </Fragment>
  )
}

export default ManagementOrderDetails;
